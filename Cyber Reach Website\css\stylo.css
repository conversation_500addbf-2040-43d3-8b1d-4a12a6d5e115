/* Custom CSS with Color Palette for Cyber Calendar Page */

/* Reset default styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

/* Body styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #061735; /* Dark blue */
    color: #ffffff; /* White */
    margin: 0; /* Remove default margin */
}



/* Header styles */
.navbar {
    position: fixed; /* Keep the position fixed */
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000; /* Ensure navbar is above other content */
    background-color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    font-style: normal;
}

.nav-list.active {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 75px;
    left: -5px;
    width: 100%;
    background-color: #1D3670;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    margin-top: 20px;
    z-index: 1;
    padding-left: 2px;
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    font-style: normal;
}

.logo-container img {
    height: 60px; /* Increased logo size to 60px */
    margin-right: 10px;
}

.menu-icon {
    font-size: 24px;
    color: black;
    cursor: pointer;
    display: none;
}

.nav-list {
    list-style-type: none;
    display: flex;
    align-items: center;
}

.nav-list li {
    margin-right: 20px;
}

.nav-list a {
    text-decoration: none;
    color: black;
    transition: color 0.3s ease-in-out;
}

.nav-list a:hover {
    color: #FFD700; /* Gold/Yellow */
}

@media only screen and (max-width: 768px) {
    .nav-list {
        display: none;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 60px;
        left: 0;
        width: 100%;
        background-color: #1D3670;
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
        margin: 3px; /* Adjust the margin as needed */
        z-index: 1;
    }

    .nav-list.active {
        display: flex; /* Show nav list when active */
    }

    .menu-icon {
        display: block;
    }
}

/* Main section styles */
main {
    padding: 20px;
    padding-top: 15vh;
}

h1 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 48px;
    color: #00adb5; /* Dark red */
    margin-bottom: 20px;
    text-align: center;
}

p {
    font-family: 'Noto Sans', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Calendar container styles */
#october23-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px; /* Add some space below the calendar */
}

/* Left calendar styles */
#leftcal {
    width: 50%;
    padding: 20px;
}

#leftcal h1 {
    color: #CC0001; /* Dark red */
    font-size: 24px;
    margin-bottom: 10px; /* Add some space below the heading */
    text-align: center;
}

#leftcal h3 {
    color: #CC0001; /* Dark red */
    font-size: 18px;
    padding: 10px;
    border-radius: 20px;
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
    margin-bottom: 10px; /* Add some space between events */
    text-align: center;
}

#leftcal h3:hover {
    background-color: #AF0000; /* Red */
}

/* Right calendar styles */
#rightcal {
    width: 50%;
    padding: 20px;
}

#rightcal table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

#rightcal th, #rightcal td {
    border: 1px solid #ccc;
    padding: 10px;
    text-align: center;
}

#rightcal th {
    background-color: #CC0001; /* Dark red */
    color: #ffffff; /* White */
}

#rightcal td {
    background-color: #061735; /* Dark blue */
}

#rightcal #borCol {
    border-left: none;
    border-right: none;
}

#rightcal #eve {
    color: #CC0001; /* Dark red */
}

.footer {
    color: #ffffff; /* White */
    padding: 30px 0;
    transition: opacity 1s ease, transform 1s ease;
}

.footer-content {
    color: #ffffff; /* White */
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
}

.follow-us, .contact-us {
    flex: 1;
    margin: 0 20px;
}

.follow-us h3, .contact-us h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    gap: 10px;
}

.social-icons img {
    width: 30px; /* Adjust the size as needed */
    height: 30px;
}

.email-icon img {
    width: 25px; /* Adjust the size as needed */
    height: 25px;
    margin-right: 10px;
}

.email-icon a {
    color: #ffffff; /* White */
    text-decoration: none;
}

.copyright {
    text-align: center;
    margin-top: 20px;
}

/* Custom footer styles */
.white-footer {
    margin-top: 20px;
    background-color: #1E90FF; /* Dark blue */
    color: #ffffff; /* White */
    border-top-left-radius: 40px;
    border-top-right-radius: 40px; /* Apply rounded edges to the top-left corner */
    overflow: hidden; /* Ensure content does not overflow rounded edges */
}

/* Responsive Styles */
@media only screen and (max-width: 768px) {
    header img {
        width: 100px;
    }

    h1 {
        font-size: 36px;
    }

    nav a {
        font-size: 16px;
    }

    #leftcal, #rightcal {
        width: 100%;
    }
}

h1, h2, h3 {
    font-family: 'Montserrat', sans-serif;
}

/* Body Text */
p {
    font-family: 'Roboto', sans-serif;
}

/* Navigation Links */
nav a {
    font-family: 'Lato', sans-serif;
}

#calender {
    width: 80%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}