@echo off
echo ========================================
echo    CybeReach GitHub Upload Script
echo ========================================
echo.

echo 🚀 Initializing Git repository...
git init

echo 📁 Adding all files...
git add .

echo 💾 Creating initial commit...
git commit -m "Initial commit: CybeReach website with news integration and GitHub Pages support"

echo 🔗 Adding GitHub remote...
echo.
echo ⚠️  Enter your GitHub username (the one you just created the repository with):
set /p username="GitHub username: "
echo.
echo 🔗 Connecting to: https://github.com/%username%/CybeReach.git
git remote add origin https://github.com/%username%/CybeReach.git

echo 📤 Pushing to GitHub...
git branch -M main
git push -u origin main

echo.
echo ✅ Upload complete!
echo.
echo 🌐 Your website will be available at:
echo    https://%username%.github.io/CybeReach/
echo.
echo 📋 Next steps:
echo    1. Go to https://github.com/%username%/CybeReach
echo    2. Click Settings > Pages
echo    3. Select "Deploy from a branch"
echo    4. Choose "main" branch and "/ (root)" folder
echo    5. Click Save
echo.
echo 🎉 Your website will be live in a few minutes!
echo.
pause
