/* Reset default styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Set fonts */
body {
    font-family: 'Open Sans', sans-serif;
    background-color: lightgray; /* Light Gray */
}

h1, h3 {
    font-family: 'Raleway', sans-serif;
}

/* Header styles */
header {
    font-family: 'Montserrat', sans-serif;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2vh 4vw; /* Adjusted for better responsiveness */
    background-color: #f2f2f2;
}

.logo-container img {
    width: 8vw; /* Adjusted for better responsiveness */
}

nav ul {
    list-style-type: none;
    display: flex;
}

nav ul li {
    margin-right: 2vw;
}

nav ul li:last-child {
    margin-right: 0;
}

nav ul li a {
    text-decoration: none;
    color: black;
}

/* Hero section styles */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(../images/Untitled\ design.jpg);
    background-size: cover;
    height: 40vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Raleway', sans-serif;
}

.hero-text {
    text-align: center;
}

.hero-text h1 {
    color: white;
    font-size: 10vw; /* Adjusted for better responsiveness */
    font-weight: bold;
    margin-bottom: 1vh;
    color: white;
    font-family: 'Montserrat', sans-serif;
}

.hero-text h3 {
    color: white;
    font-family: silkscreen;
    font-size: 2.5vw; /* Adjusted for better responsiveness */
}

/* Main section styles */
main {
    padding: 4vw;
    font-family: 'Open Sans', sans-serif;
}

section {
    margin-bottom: 4vw;
    background-color: #fff; /* White */
    border-radius: 1vw;
    overflow: hidden;
}

section h2 {
    font-size: 3.6vw;
    font-weight: bold;
    margin-bottom: 2vw;
}

section img {
    width: 100%;
    max-width: 100%;
    height: auto;
    margin-bottom: 2vw;
}

section p {
    margin-bottom: 2vw;
}

section a {
    display: inline-block;
    padding: 1vw 2vw;
    background-color: white;
    color: black;
    text-decoration: none;
    border-radius: 0.5vw;
}

/* Contact info section styles */
#contact-info {
    font-family: 'Roboto', sans-serif;
    background-color: #f2f2f2;
    padding: 4vw;
}

#contact-info ul {
    list-style-type: none;
    margin-bottom: 2vw;
}

#contact-info ul li {
    margin-bottom: 1vw;
}

#contact-info a {
    margin-top: 1vw;
}

/* Media queries for improved responsiveness */
@media (max-width: 768px) {
    .logo-container img {
        width: 20vw; /* Adjusted for smaller screens */
    }

    nav ul {
        flex-direction: column;
    }

    nav ul li {
        margin-right: 0;
        margin-bottom: 2vh;
    }

    main {
        padding: 2vw;
    }

    section h2 {
        font-size: 5vw; /* Adjusted for smaller screens */
        margin-bottom: 1.5vw;
    }

    section img {
        margin-bottom: 1.5vw;
    }

    section p,
    section a {
        margin-bottom: 2vw;
    }

    #contact-info ul li {
        margin-bottom: 1.5vw;
    }
}

/* Existing styles remain unchanged */

/* Block section styles */
.block-section {
    font-family: 'Nunito', sans-serif;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.block-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
}

.block-icon {
    width: 20vw; /* Adjust icon size as needed */
    margin-right: 2vw; /* Adjust spacing between icon and text */
}

.block-text {
    text-align: left;
}

/* Additional styling to enhance attractiveness (adjust as needed) */
.block-content {
    background-color: #0c3a6f; /* Dark Blue */
    color: #f2f2f2; /* Light Gray */
    padding: 2vw;
    border-radius: 1vw;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease-in-out;
}

.block-content:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}

#cyber-calender .block-text,
#blogs .block-text {
    text-align: right;
}

a:hover {
    color: #0c3a6f; /* Dark Blue */
}

a {
    color: #a4123f; /* Red */
    transition: color 0.3s ease-in-out;
}
