@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.logo-container1 img {
  width: 80vh;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5)); /* Apply drop shadow */
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Poppins', sans-serif;
  background-image: linear-gradient(80deg, #003A6D, #07264e);
  color: white;
  width: 100vw; /* Set a fixed width equal to the viewport width */
  overflow-x: hidden; /* Hide horizontal overflow */
}

.navbar {
  position: relative;
  z-index: 2;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-container img {
  height: 60px; /* Increased logo size to 60px */
  margin-right: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.95));
}

.nav-list {
  list-style-type: none;
  display: flex;
  align-items: center;
}

.nav-list li {
  margin-right: 20px;
}

.nav-list a {
  text-decoration: none;
  color: white;
  transition: color 0.3s ease-in-out;
}

.nav-list a:hover {
  color: #FFD700; /* Gold/Yellow */
}

.nav-logo {
  height: 80px; /* Increase logo size in the navigation */
}

.entrance-animation {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 50px 20px; /* Adjust padding as needed */
}

.left-content {
  flex: 1;
  animation: slideInLeft 1s ease-out;
}

.right-content {
  flex: 1;
  animation: slideInRight 1s ease-out;
}

.animation-logo {
  max-width: 100%;
  height: auto;
  animation: fadeInUp 1.2s ease-out; /* You can adjust animation properties */
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.footer {
  
  color: black;
  padding: 60px 0;
  transition: opacity 1s ease, transform 1s ease;
}

.blue-content {
  margin-top: 20px;
  background-color: #1E90FF;
  border-top-left-radius: 40px;
  border-top-right-radius: 40px; /* Apply rounded edges to the top-left corner */
  overflow: hidden; 
}

.footer-content {
  color: black;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.follow-us,
.contact-us {
  flex: 1;
  margin: 0 20px;
}

.follow-us h3,
.contact-us h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.social-icons {
  display: flex;
  gap: 10px;
}

.social-icons img {
  width: 30px; /* Adjust the size as needed */
  height: 30px;
}

.email-icon img {
  width: 25px; /* Adjust the size as needed */
  height: 25px;
  margin-right: 10px;
}

.email-icon a {
  color: black;
  text-decoration: none;
}

.copyright {
  text-align: center;
  margin-top: 20px;
}

h2 {
  font-size: 6rem;
}

.left-content p {
  font-size: 1rem;
}

@media only screen and (max-width: 768px) {
  .navbar {
    padding: 10px;
  }

  .logo-container img {
    height: 60px; /* Adjust logo size for smaller screens */
  }

  .nav-list li {
    margin-right: 10px;
  }

  .footer-content {
    margin-top: 20px;
    flex-direction: column;
    align-items: center;
  }

  .follow-us,
  .contact-us {
    margin: 10px 0;
    text-align: center;
  }

  .animation-logo {
    max-width: 110%; /* Adjust max-width for smaller screens */
  }

  .left-content h2 {
    font-size: 2.2rem; /* Adjust font size for smaller screens */
    margin-bottom: 20px; /* Adjust margin for spacing */
  }

  .left-content p {
    font-size: 0.75rem;
  }
}

@media only screen and (max-width: 425px) {
  .navbar {
    padding: 5px;
  }

  .logo-container img {
    height: 30px; /* Adjust logo size for 425px screens */
  }

  .nav-list li {
    margin-right: 5px;
  }

  .animation-logo {
    max-width: 130%; /* Adjust max-width for smaller screens */
  }
  
  .left-content h2 {
  font-size: 1.9rem; /* Adjust font size for 425px screens /
  margin-bottom: 15px; / Adjust margin for spacing */
  }
  
  .left-content p {
  font-size: 0.6rem;
  }
  }
  
  /* Responsive Styles for 375px */
  @media only screen and (max-width: 375px) {
  .navbar {
  padding: 5px;
  }
  
  .logo-container img {
  height: 25px; /* Adjust logo size for 375px screens */
  }
  
  .nav-list li {
  margin-right: 5px;
  }
  
  .animation-logo {
  max-width: 80%; /* Adjust max-width for smaller screens */
  }
  
  .left-content h2 {
  font-size: 1.2rem; /* Adjust font size for 375px screens /
  margin-bottom: 10px; / Adjust margin for spacing */
  }
  
  .left-content p {
  font-size: 1.2rem;
  }
  }
  
  /* Responsive Styles for 320px */
  @media only screen and (max-width: 320px) {
  .navbar {
  padding: 5px;
  }
  
  .logo-container img {
  height: 20px; /* Adjust logo size for 320px screens */
  }
  
  .nav-list li {
  margin-right: 5px;
  }
  
  .animation-logo {
  max-width: 150%; /* Adjust max-width for smaller screens */
  }
  
  .left-content h2 {
  font-size: 1.5rem; /* Adjust font size for 320px screens /
  margin-bottom: 5px; / Adjust margin for spacing */
  }
  
  .left-content p {
  font-size: 0.5rem;
  }
  
  .left-content p {
  font-size: 0.5rem;
  }
  }

  .team-heading {
    text-align: center;
    margin-top: 50px;
    font-size: 2.5rem; /* Increased font size */
  }
  
  .team-showcase {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 40px; /* Increased gap between team members */
    margin-top: 30px;
  }
  
  .team-member {
    width: 300px; /* Adjust width as needed */
    height: auto; /* Allow height to adjust based on content */
    background-color: #f9f9f9;
    border-radius: 10px;
    overflow: hidden; /* Hide overflowing content */
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1); /* Box shadow */
    display: flex; /* Use flexbox for layout */
    flex-direction: column; /* Align content vertically */
  }
  
  .team-member img {
    width: 100%;
    height: 200px; /* Set a fixed height for all images */
    object-fit: cover; /* Maintain aspect ratio and cover container */
  }
  
  .member-info {
    padding: 20px; /* Increased padding */
  }
  
  .member-info p {
    opacity: 1; /* Ensure the content is always visible */
    transition: none; /* Remove transition effect */
  }
  
  .member-info h2 {
    color: black;
    margin-top: 0;
    font-size: 1.5rem; /* Increased font size */
  }
  
  .member-info h3 {
    margin-top: 10px; /* Increased margin */
    margin-bottom: 15px; /* Increased margin */
    font-size: 1.2rem; /* Increased font size */
    color: #888;
  }
  
  .member-info p {
    font-size: 1rem; /* Increased font size */
    color: #666;
  }
  
  .team-member:hover {
    background-color: #e0e0e0; /* Change background color on hover */
  }
  
  .team-member:hover .member-info p {
    opacity: 1;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .team-member {
    animation: fadeInUp 0.5s ease-out;
  }

  #up {
    position: absolute;
    height: 800px;
    width: 800px;
    border-radius: 50%;
    background-image: linear-gradient(80deg, #CC0001, #AF0000);
    z-index: -2;
    filter: blur(80px);
    animation: down 40s infinite;
  }
  
  #down {
    position: absolute;
    right: 0;
    height: 500px;
    width: 500px;
    border-radius: 50%;
    background-image: linear-gradient(80deg, #07264e, #003A6D);
    z-index: -1;
    filter: blur(80px);
    animation: up 30s infinite;
  }
  
  #left {
    position: absolute;
    height: 500px;
    width: 500px;
    border-radius: 50%;
    background-image: linear-gradient(80deg, #AF0000, #003A6D);
    z-index: -1;
    filter: blur(80px);
    animation: left 30s 1s infinite;
  }
  
  #right {
    position: absolute;
    height: 500px;
    width: 500px;
    border-radius: 50%;
    background-image: linear-gradient(80deg, #CC0001, #003A6D);
    z-index: -1;
    filter: blur(80px);
    animation: right 40s .5s infinite;
  }
  
  

  @keyframes down {
    0%, 100% {
      top: -100px;
    }
    70% {
      top: 700px;
    }
  }
  
  @keyframes up {
    0%, 100% {
      bottom: -100px;
    }
    70% {
      bottom: 700px;
    }
  }
  
  @keyframes left {
    0%, 100% {
      left: -100px;
    }
    70% {
      left: 1300px;
    }
  }
  
  @keyframes right {
    0%, 100% {
      right: -100px;
    }
    70% {
      right: 1300px;
    }
  }
 

