/*!
 * Evo Calendar - Simple and Modern-looking Event Calendar Plugin
 *
 * Licensed under the MIT License
 * 
 * Version: 1.1.2
 * Author: <PERSON><PERSON>
 * Docs: https://edlynvillegas.github.com/evo-calendar
 * Repo: https://github.com/edlynvillegas/evo-calendar
 * Issues: https://github.com/edlynvillegas/evo-calendar/issues
 * 
*/
.midnight-blue{-webkit-box-shadow:0 10px 50px -20px rgba(19,31,51,.65);box-shadow:0 10px 50px -20px rgba(19,31,51,.65);border:2px solid #222831;color:#EEE}.midnight-blue .calendar-sidebar{background:#222831;border-right:2px solid #222831;-webkit-box-shadow:0 0 18px -3px rgba(0,0,0,.65);box-shadow:0 0 18px -3px rgba(0,0,0,.65)}.midnight-blue.sidebar-hide .calendar-sidebar{-webkit-box-shadow:none;box-shadow:none}.midnight-blue .calendar-sidebar>.month-list::-webkit-scrollbar-thumb:hover{background:#cee1ff}.midnight-blue .calendar-sidebar>.month-list>.calendar-months>li{text-transform:uppercase}.midnight-blue .calendar-sidebar>.month-list>.calendar-months>li:hover{background-color:rgba(255,255,255,.2)}.midnight-blue .calendar-sidebar>.month-list>.calendar-months>li.active-month{background-color:#00adb5;font-weight:600}.midnight-blue .calendar-sidebar>span#sidebarToggler,.midnight-blue #eventListToggler{background-color:#222831;-webkit-box-shadow:0 0 18px -3px rgba(0,0,0,.65);box-shadow:0 0 18px -3px rgba(0,0,0,.65)}.midnight-blue .calendar-sidebar>span#sidebarToggler{right:-2px}.midnight-blue button.icon-button>span.bars{background-color:#00adb5}.midnight-blue button.icon-button>span.bars::before{background-color:#12cbd4}.midnight-blue button.icon-button>span.bars::after{background-color:#027e84}.midnight-blue .calendar-sidebar>.calendar-year>button.icon-button>span,.midnight-blue button.icon-button>span.chevron-arrow-right{display:block;border-right:6px solid #00adb5;border-bottom:6px solid #027e84}.midnight-blue .calendar-sidebar>.calendar-year>button.icon-button>span{border-width:4px}.midnight-blue .calendar-inner{background-color:#393e46;-webkit-box-shadow:5px 0 18px -3px rgba(0,0,0,.35);box-shadow:5px 0 18px -3px rgba(0,0,0,.35)}.midnight-blue .calendar-inner td{color:#fff}.midnight-blue th[colspan="7"]{position:relative;font-size:30px;color:#00adb5}.midnight-blue th[colspan="7"]::after{content:'';display:block;width:92%;height:1px;margin:0 auto;background:#027e84}.midnight-blue tr.calendar-body .calendar-day .day:hover{background-color:rgba(255,255,255,.12);color:#00f4ff}.midnight-blue tr.calendar-body .calendar-day .day.calendar-today{background:#00adb5}.midnight-blue tr.calendar-body .calendar-day .day{border-width:2px;border-radius:0;color:#fff}.midnight-blue tr.calendar-body .calendar-day .day.calendar-active,.midnight-blue tr.calendar-body .calendar-day .day.calendar-active:hover{border-color:#EEE;color:#EEE}.midnight-blue tr.calendar-body .calendar-day .day.calendar-today:hover{color:#EEE}.midnight-blue .calendar-events{background-color:#192435;padding:70px 20px 60px 20px}.midnight-blue .calendar-events::-webkit-scrollbar-thumb{background:#fff;border-radius:5px}.midnight-blue .calendar-events::-webkit-scrollbar-thumb:hover{background:#cee1ff}.midnight-blue .calendar-events p{color:#EEE}.midnight-blue .calendar-inner::after{background-color:rgba(25,36,53,.65)}.midnight-blue .event-container{border-radius:0}.midnight-blue .event-container:hover{background-color:#5d6979}.midnight-blue .event-container>.event-icon>div{border:2px solid transparent}.midnight-blue .event-container:hover>.event-icon>div{border-color:#fff}.midnight-blue .event-container>.event-icon>div.event-bullet-birthday,.midnight-blue .event-indicator>.type-bullet>div.type-birthday{background-color:#6c72bf}.midnight-blue .event-container>.event-info>p.event-title>span{color:#fff;border-radius:0;background-color:rgba(0,173,181,.15);border:1px solid #00adb5}.midnight-blue .event-container:hover>.event-info>p.event-title>span{background-color:rgb(0 173 181)}.midnight-blue .event-list>.event-empty{background-color:rgba(0,173,181,.15);border:1px solid #00adb5}.midnight-blue .event-list>.event-empty>p{color:#fff}@media only screen and (max-width:768px){.midnight-blue .event-indicator{-webkit-transform:translate(-50%,-100%);-ms-transform:translate(-50%,-100%);transform:translate(-50%,-100%)}.midnight-blue .calendar-events{-webkit-box-shadow:-5px 0 18px -3px rgba(0,0,0,.65);box-shadow:-5px 0 18px -3px rgba(0,0,0,.65)}.midnight-blue.event-hide .calendar-events{-webkit-box-shadow:none;box-shadow:none}}@media screen and (max-width:425px){.midnight-blue .calendar-sidebar{border-right:none}.midnight-blue .calendar-sidebar>.calendar-year{background-color:#222831;-webkit-box-shadow:0 3px 8px -3px rgba(0,0,0,.65);box-shadow:0 3px 8px -3px rgba(0,0,0,.65)}.midnight-blue .calendar-sidebar>.month-list{background-color:#192435}.midnight-blue .calendar-sidebar>span#sidebarToggler{right:0}.midnight-blue #eventListToggler,.midnight-blue .calendar-sidebar>span#sidebarToggler{-webkit-box-shadow:none;box-shadow:none}.midnight-blue .calendar-inner{-webkit-box-shadow:5px 0 18px -3px rgba(0,0,0,.35);box-shadow:0 5px 18px -3px rgba(0,0,0,.65)}.midnight-blue tr.calendar-body .calendar-day .day.calendar-active,.midnight-blue tr.calendar-body .calendar-day .day.calendar-active:hover{border-width:1px}.midnight-blue tr.calendar-body .calendar-day .day.calendar-today .event-indicator{-webkit-transform:translate(-50%,3px);-ms-transform:translate(-50%,3px);transform:translate(-50%,3px)}.midnight-blue tr.calendar-body .calendar-day .day.calendar-active .event-indicator{-webkit-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.midnight-blue .event-indicator{-webkit-transform:translate(-50%,0);-ms-transform:translate(-50%,0);transform:translate(-50%,0)}.midnight-blue .calendar-events{padding:20px 15px}.midnight-blue.event-hide .calendar-events{padding:0 15px}}